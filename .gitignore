# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Idea
/.idea/AugmentWebviewStateStore.xml
/.idea/codeStyles/codeStyleConfig.xml
/.idea/material_theme_project_new.xml
/.idea/modules.xml
/.idea/codeStyles/Project.xml
/.idea/inspectionProfiles/Project_Default.xml
/.idea/ultimatetcgcm-with-cl-ci.iml
/.idea/vcs.xml

.env.local
/.idea/compiler.xml
/.idea/jsLibraryMappings.xml
/.idea/ultimatetcgcm-vercel.iml
