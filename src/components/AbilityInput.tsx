"use client";
import "@mantine/core/styles.css";

import "@mantine/tiptap/styles.css";

import { TextStyle } from "@tiptap/extension-text-style";
import { Bold } from "@tiptap/extension-bold";
import { useDispatch, useSelector } from "react-redux";
import { RichTextEditor, useRichTextEditorContext } from "@mantine/tiptap";
import React, { useEffect, useRef } from "react";
import {
  onAbility,
  onAbilityDropShadow,
  onAbilityTextSize,
  onDropShadow,
  onEditorState,
  onInputField,
} from "@/store/formSlice";
import { storeState } from "@/store/store";
import { StarterKit } from "@tiptap/starter-kit";
import { Underline } from "@tiptap/extension-underline";
import Highlight from "@tiptap/extension-highlight";
import { TextAlign } from "@tiptap/extension-text-align";
import { Color } from "@tiptap/extension-color";
import { BubbleMenu, SingleCommands, useEditor } from "@tiptap/react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { useState } from "react";
import { Italic } from "@tiptap/extension-italic";
import {
  Button,
  MantineProvider,
  Tooltip as MantineTooltip,
} from "@mantine/core";

const CustomParagraph = TextStyle.extend({
  renderHTML({ HTMLAttributes }) {
    if (HTMLAttributes.class === "orange-ability") {
      return [
        "span",
        { class: "orange-ability-container" },
        [
          "span",
          { class: "orange-ability-shadow" },
          ["span", { ...HTMLAttributes }, 0],
        ],
      ];
    } else if (HTMLAttributes.class === "black-ability") {
      return [
        "span",
        { class: "black-ability-container" },
        [
          "span",
          { class: "black-ability-shadow" },
          ["span", { ...HTMLAttributes }, 0],
        ],
      ];
    } else if (HTMLAttributes.class === "trigger-ability") {
      return [
        "span",
        { class: "trigger-ability-container" },
        [
          "span",
          { class: "trigger-ability-shadow" },
          ["span", { ...HTMLAttributes }, 0],
        ],
      ];
    } else {
      return ["span", { ...HTMLAttributes }, 0];
    }
  },
  addAttributes() {
    return {
      color: {
        // … and customize the HTML rendering.
        renderHTML: (attributes) => {
          const color = attributes.color;
          const classColor =
            color === "#2F77B3"
              ? "blue-ability"
              : color === "#d94880"
                ? "pink-ability"
                : color === "#DC8535"
                  ? "orange-ability"
                  : color === "#ba212f"
                    ? "red-ability"
                    : color === "#f8ed70"
                      ? "trigger-ability"
                      : color === "#FFFFFF"
                        ? "white-ability"
                        : color === "#000000"
                          ? "black-ability"
                          : "";
          return {
            class: classColor,
          };
        },
      },
    };
  },
});

const CustomBold = Bold.extend({
  name: "customBold",
  renderHTML({ HTMLAttributes }) {
    // Original:
    // return ['strong', HTMLAttributes, 0]
    return ["b", HTMLAttributes, 0];
  },
});
const CustomItalic = Italic.extend({
  name: "customItalic",
  renderHTML({ HTMLAttributes }) {
    const attributes = HTMLAttributes;
    attributes.class = `font-one-piece-italic-bold text-[0.94em]`;
    // Original:
    // return ['strong', HTMLAttributes, 0]
    return ["em", attributes, 0];
  },
});
function DropShadowButton() {
  const dispatch = useDispatch();
  const dropShadow = useGetStoreState("dropShadow");

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <RichTextEditor.Control
          onClick={() => {
            dispatch(onDropShadow());
          }}
          className={`px-2! ${dropShadow ? "bg-neutral-800! text-neutral-50! transition-all duration-100 dark:bg-neutral-200! dark:text-neutral-950!" : ""}`}
        >
          Text Outline
        </RichTextEditor.Control>
      </TooltipTrigger>
      <TooltipContent>Toggle text outline</TooltipContent>
    </Tooltip>
  );
}
function AbilityDropShadowButton() {
  const dispatch = useDispatch();
  const abilityDropShadow = useGetStoreState("abilityDropShadow");
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <RichTextEditor.Control
          onClick={() => {
            dispatch(onAbilityDropShadow());
          }}
          className={`px-2! ${abilityDropShadow ? "bg-neutral-800! text-neutral-50! transition-all duration-100 dark:bg-neutral-200! dark:text-neutral-950!" : ""}`}
        >
          Ability Outline
        </RichTextEditor.Control>
      </TooltipTrigger>
      <TooltipContent>Toggle ability outline</TooltipContent>
    </Tooltip>
  );
}
function Background() {
  const dispatch = useDispatch();
  const abilityBackground = useGetStoreState("abilityBackground");
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <RichTextEditor.Control
          onClick={() => {
            dispatch(onInputField("abilityBackground"));
          }}
          className={`px-2! ${abilityBackground ? "bg-neutral-800! text-neutral-50! transition-all duration-100 dark:bg-neutral-200! dark:text-neutral-950!" : ""}`}
        >
          Background
        </RichTextEditor.Control>
      </TooltipTrigger>
      <TooltipContent>Toggle ability background</TooltipContent>
    </Tooltip>
  );
}

function TextIncreaseDecrease({
  children,
  by,
  label,
}: {
  children: React.ReactNode;
  by: number;
  label: string;
}) {
  const dispatch = useDispatch();
  const abilityTextSize = useGetStoreState("abilityTextSize");
  return (
    <RichTextEditor.Control
      onClick={() => {
        dispatch(onAbilityTextSize(by));
      }}
      aria-label={label}
      title={label}
      className={"px-2!"}
      disabled={abilityTextSize === 1 && label === "Decrease font size"}
    >
      {children}
    </RichTextEditor.Control>
  );
}
function TextSize() {
  const size = useSelector(
    (state: storeState) => state.mainFormSlice.abilityTextSize,
  );
  const dispatch = useDispatch();
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <RichTextEditor.Control
          onClick={() => {
            dispatch(onAbilityTextSize("default"));
          }}
          aria-label="Reset font size"
          // title="Reset font size"
          className={""}
        >
          <div
            className={
              "flex min-w-[4rem] content-center justify-center text-center"
            }
          >
            <p>{size + "px"}</p>
          </div>
        </RichTextEditor.Control>
      </TooltipTrigger>
      <TooltipContent>Reset font size</TooltipContent>
    </Tooltip>
  );
}
export default function AbilityInput({
  backgroundToggle = false,
}: {
  backgroundToggle?: boolean;
}) {
  const editorState = useGetStoreState("editorState");
  const dispatch = useDispatch();

  const [openedHelp, setOpenedHelp] = useState(false);
  const [openedAbilities, setOpenedAbilities] = useState(false);

  const openHelp = () => setOpenedHelp(true);
  const openAbilities = () => setOpenedAbilities(true);

  // const notStageRoute = route.Characters !== "Stage";
  // const notEventRoute = route.Characters !== "Event";
  // const notDonRoute = route.Characters !== "Don";
  const editor = useEditor({
    content: editorState,
    immediatelyRender: false,
    extensions: [
      StarterKit,
      Underline,
      // Superscript,
      // SubScript,
      Highlight,
      TextAlign.configure({ types: ["heading", "paragraph"] }),
      Color,
      CustomParagraph,
      CustomBold,
      CustomItalic,
    ],
    editorProps: {
      attributes: {
        class: "ability-input",
      },
    },
    onUpdate({ editor }) {
      dispatch(onAbility(editor.getHTML()));
    },
    onDestroy() {
      dispatch(onEditorState(editor?.getJSON()));
    },
    onBlur() {
      dispatch(onEditorState(editor?.getJSON()));
    },
    onCreate({ editor }) {
      editor.commands.setContent(editorState);
    },
  });
  return (
    <>
      <MantineProvider
        defaultColorScheme={"auto"}
        theme={{
          components: {
            Button: Button.extend({
              styles: {
                inner: { fontWeight: 500 },
              },
            }),
          },
        }}
      >
        <div className={"flex flex-col"}>
          <label className={"text-sm"}>Ability</label>
          <p className={"text-xs text-[#868e96]"}>Card ability</p>
          <RichTextEditor
            editor={editor}
            className={`relative mt-1 max-w-full bg-stone-800 lg:max-w-[697.42px]`}
            // classNames={{
            //   content: "dark:bg-background!",
            //   toolbar: "dark:bg-background!",
            //   control:
            //     "dark:bg-card! dark:hover:bg-full-button-hover! transition-all duration-100!",
            // }}
            id={"ability"}
          >
            <RichTextEditor.Toolbar className={"flex flex-row"}>
              <RichTextEditor.ControlsGroup>
                <RichTextEditor.Bold />
                <RichTextEditor.Italic />
                <RichTextEditor.Underline />
                <RichTextEditor.Strikethrough />
                <RichTextEditor.ClearFormatting />
              </RichTextEditor.ControlsGroup>
              <RichTextEditor.ColorPicker
                colors={[
                  "#25262b",
                  "#868e96",
                  "#fa5252",
                  "#e64980",
                  "#be4bdb",
                  "#7950f2",
                  "#4c6ef5",
                  "#228be6",
                  "#15aabf",
                  "#12b886",
                  "#40c057",
                  "#82c91e",
                  "#fab005",
                  "#fd7e14",
                ]}
              />

              {editor && (
                <BubbleMenu
                  editor={editor}
                  tippyOptions={{ offset: [80, -60], zIndex: 10 }}
                >
                  <RichTextEditor.ControlsGroup className={"flex"}>
                    <MantineTooltip
                      label={"OnPlay"}
                      className={
                        "blue-ability-tooltip z-50 bg-neutral-100 text-neutral-900 dark:text-neutral-100"
                      }
                    >
                      <RichTextEditor.Color color="#2F77B3" />
                    </MantineTooltip>
                    <MantineTooltip
                      label={"Once Per Turn"}
                      className={
                        "pink-ability-tooltip z-50 bg-neutral-100 text-neutral-900 dark:text-neutral-100"
                      }
                    >
                      <RichTextEditor.Color color="#d94880" />
                    </MantineTooltip>
                    <MantineTooltip
                      label={"Blocker"}
                      className={
                        "orange-ability-tooltip z-50 bg-neutral-100 text-neutral-900 dark:text-neutral-100"
                      }
                    >
                      <RichTextEditor.Color color="#DC8535" />
                    </MantineTooltip>
                    <MantineTooltip
                      label={"Counter"}
                      className={
                        "counter-ability-tooltip z-50 bg-neutral-100 text-neutral-900 dark:text-neutral-100"
                      }
                    >
                      <RichTextEditor.Color color="#ba212f" />
                    </MantineTooltip>
                    <MantineTooltip
                      label={"Trigger"}
                      className={
                        "trigger-ability-tooltip z-50 bg-neutral-100 text-neutral-900 dark:text-neutral-100"
                      }
                    >
                      <RichTextEditor.Color color="#f8ed70" />
                    </MantineTooltip>
                    <MantineTooltip
                      label={"1"}
                      className={
                        "number-ability-tooltip z-50 bg-neutral-100 text-neutral-900 dark:text-neutral-100"
                      }
                    >
                      <RichTextEditor.Color color="#FFFFFF" />
                    </MantineTooltip>
                    <MantineTooltip
                      label={"DON!!×1"}
                      className={
                        "number-ability-tooltip z-50 bg-neutral-100 text-neutral-900 dark:text-neutral-100"
                      }
                    >
                      <RichTextEditor.Color color="#000000" />
                    </MantineTooltip>
                    <RichTextEditor.Italic />
                    <RichTextEditor.UnsetColor />
                  </RichTextEditor.ControlsGroup>
                </BubbleMenu>
              )}
              <RichTextEditor.UnsetColor />
              <RichTextEditor.ControlsGroup>
                <TextIncreaseDecrease by={-0.5} label={"Decrease font size"}>
                  {"<"}
                </TextIncreaseDecrease>
                <TextSize />
                <TextIncreaseDecrease by={0.5} label={"Increase font size"}>
                  {">"}
                </TextIncreaseDecrease>
              </RichTextEditor.ControlsGroup>
              <DropShadowButton />
              <AbilityDropShadowButton />
              {backgroundToggle && <Background />}
              <Dialog open={openedHelp} onOpenChange={setOpenedHelp}>
                <DialogContent
                  className="flex flex-col gap-0 sm:max-w-lg"
                  disableCloseIcon
                >
                  {/*TODO: Carousel*/}
                  <DialogTitle className="sr-only">Help</DialogTitle>
                  <p>
                    Just select the text to style it, you can style both Trigger
                    and Ability text.
                  </p>
                  <br />
                  <p>
                    A bubble menu that includes all ability styles represented
                    by their respective colors will appear, you can also remove
                    the style by selecting the last option in the menu.
                  </p>
                  <br />
                  <p>
                    For trigger text, pres &quot;Enter&quot; to create a new
                    line in case you want the text to start bellow{" "}
                    <span className="trigger-text">
                      <span className="trigger-ability-container">
                        <span className="trigger-ability-shadow">
                          <span className="trigger-ability mt-1">Trigger</span>{" "}
                        </span>
                      </span>
                    </span>
                  </p>
                  <br />
                  <p>
                    You can use the <strong>&quot;Background&quot;</strong>{" "}
                    button to toggle the background of the ability text.
                  </p>
                  <br />
                  <p>
                    You can use the <strong>&quot;Text Outline&quot;</strong>{" "}
                    and <strong>&quot;Ability Outline&quot;</strong> buttons to
                    toggle the white outline of the text and the ability text
                    respectively.
                  </p>
                  <br />
                  <p>
                    You can change the font size by pressing{" "}
                    <strong>{`"<"`}</strong> or <strong>{`">"`}</strong>, and
                    you can reset it by pressing the current font size button.
                  </p>
                  <br />
                  <p>
                    Pro Tip: you can use the &quot;×&quot; sign instead of
                    &quot;x&quot; to make the DON!! ability more accurate e.g.{" "}
                    <span className="black-ability-container">
                      <span className="black-ability-shadow">
                        <span className="black-ability">DON!!×1</span>{" "}
                      </span>
                    </span>
                  </p>
                </DialogContent>
              </Dialog>
              <Dialog open={openedAbilities} onOpenChange={setOpenedAbilities}>
                <DialogContent className="flex flex-col gap-1 sm:max-w-lg">
                  <DialogTitle className="sr-only">Ability List</DialogTitle>
                  <p>
                    <span className={"blue-ability"}>On Play</span>{" "}
                    <span className={"blue-ability"}>On Block</span>{" "}
                    <span className={"blue-ability"}>On K.O.</span>{" "}
                    <span className={"blue-ability"}>Activate:Main</span>{" "}
                    <span className={"blue-ability"}>When Attacking</span>{" "}
                    <span className={"blue-ability"}>Your Turn</span>{" "}
                    <span className={"blue-ability"}>Opponent&apos;s turn</span>{" "}
                    <span className={"blue-ability"}>Main</span>{" "}
                    <span className={"blue-ability"}>When Attacked</span>{" "}
                    <span className={"blue-ability"}>Start of Your Turn</span>{" "}
                    <span className={"blue-ability"}>End of Your Turn</span>{" "}
                    <span className={"blue-ability"}>
                      When Opponent Attacks
                    </span>{" "}
                  </p>
                  <p>
                    <span className={"pink-ability"}>Once Per Turn</span>{" "}
                  </p>
                  <p>
                    <span className={"orange-ability-container"}>
                      <span className={"orange-ability-shadow"}>
                        <span className={"orange-ability"}>Blocker</span>{" "}
                      </span>
                    </span>
                    <span className={"orange-ability-container"}>
                      <span className={"orange-ability-shadow"}>
                        <span className={"orange-ability"}>Rush</span>{" "}
                      </span>
                    </span>
                    <span className={"orange-ability-container"}>
                      <span className={"orange-ability-shadow"}>
                        <span className={"orange-ability"}>Double Attack</span>{" "}
                      </span>
                    </span>
                    <span className={"orange-ability-container"}>
                      <span className={"orange-ability-shadow"}>
                        <span className={"orange-ability"}>Banish</span>{" "}
                      </span>
                    </span>
                  </p>
                  <p>
                    <span className={"red-ability"}>Counter</span>{" "}
                  </p>
                  <p className={"trigger-text"}>
                    <span className={"trigger-ability-container"}>
                      <span className={"trigger-ability-shadow"}>
                        <span className={"trigger-ability mt-1"}>Trigger</span>{" "}
                      </span>
                    </span>
                  </p>
                  <p>
                    <span className={"white-ability"}>1</span>{" "}
                  </p>
                  <p>
                    <span className={"black-ability-container"}>
                      <span className={"black-ability-shadow"}>
                        <span className={"black-ability"}>DON!!×1</span>{" "}
                      </span>
                    </span>
                  </p>
                </DialogContent>
              </Dialog>
              <RichTextEditor.Control
                className={"px-0.5!"}
                onClick={openAbilities}
                title="Ability List"
              >
                Ability List
              </RichTextEditor.Control>
              <RichTextEditor.Control
                className={"px-0.5!"}
                onClick={openHelp}
                title="Help"
              >
                Help
              </RichTextEditor.Control>
            </RichTextEditor.Toolbar>

            <RichTextEditor.Content
              onChange={() => {}}
              className={`font-roboto overflow-clip`}
              //@ts-expect-error wrong type error
              classNames={{ content: "min-h-20" }}
            />
            <DefaultStateSetter />
          </RichTextEditor>
        </div>
      </MantineProvider>
    </>
  );
}

function DefaultStateSetter() {
  // Get the editor instance from the context
  const editor = useRichTextEditorContext();

  // Get the default state for the editor from some store
  const editorDefaultState = useGetStoreState("editorDefaultState");

  const commandsRef = useRef<SingleCommands>(undefined);

  // Update the ref if the editor commands change
  useEffect(() => {
    commandsRef.current = editor?.editor?.commands;
  }, [editor?.editor?.commands]);

  useEffect(() => {
    // Only set content if the commands have not changed
    if (commandsRef.current) {
      commandsRef.current.setContent(editorDefaultState);
    }
  }, [editorDefaultState]);

  // This component does not render anything
  return null;
}
