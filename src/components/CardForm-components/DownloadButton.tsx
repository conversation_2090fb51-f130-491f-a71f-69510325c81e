"use client";
import { showErrorToast } from "@/lib/toast";

import React, { useRef, useState, useEffect } from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useSelector } from "react-redux";
import { state } from "@/store/formSlice";
import { storeState } from "@/store/store";
import { Subscription } from "@/types";
import { User } from "@supabase/supabase-js";
import { usePostHog } from "posthog-js/react";
import { createClient } from "@/utils/supabase/client";
import axios from "axios";
import { useServerStatus } from "@/context/ServerStatusContext";
import { Button } from "@/components/ui/button";
import CustomLoader from "@/components/CustomLoader";

const supabase = createClient();

export default function DownloadButton({
  cardKind,
  subscription,
  user,
}: {
  cardKind: "character" | "leader" | "event" | "stage" | "don";
  subscription: Subscription;
  user:
    | {
        user: User;
      }
    | {
        user: null;
      };
}) {
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [generating, setGenerating] = useState(false);
  const [requestFullySent, setRequestFullySent] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);
  const [cardTokens, setCardTokens] = useState(subscription.cardTokens);
  const [queueStatus, setQueueStatus] = useState({ size: 0, pending: 0 });
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(
    null,
  );
  const isCropping = useGetStoreState("isCropping") as boolean;
  const isCroppingBackground = useGetStoreState(
    "isCroppingBackground",
  ) as boolean;
  const storeState = useSelector<storeState>(
    (state) => state.mainFormSlice,
  ) as state;
  const posthog = usePostHog();
  const { setServerConnectionError, setIsDismissed } = useServerStatus();

  // Function to fetch queue status
  const fetchQueueStatus = async () => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_GET_CARD_API_URL}/api/get-card`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        },
      );

      if (response.ok) {
        const data = await response.json();
        setQueueStatus(data);
        return data.size > 0 || data.pending > 0;
      }
      return false;
    } catch (error: Error | unknown) {
      console.log("Failed to fetch queue status:", error);

      // Check for connection refused error
      if (error instanceof Error) {
        if (error.name === "AbortError") {
          showErrorToast("Request timeout", {
            description: "The server is taking too long to respond.",
          });
        } else if (
          error.name === "TypeError" &&
          (error.message.includes("Failed to fetch") ||
            error.message.includes("Network request failed"))
        ) {
          showErrorToast("Failed to fetch queue status", {
            description: "Please check your internet connection and try again.",
          });
        } else {
          // ✅ Add this to catch all other errors
          showErrorToast("Queue status error", {
            description: error.message || "An unexpected error occurred.",
          });
        }
        setServerConnectionError(true);
      }

      return false;
    }
  };

  // Start polling for queue status
  const startPolling = () => {
    if (pollingInterval) return; // Don't start if already polling

    const interval = setInterval(async () => {
      const shouldContinue = await fetchQueueStatus();
      if (!shouldContinue && !generating) {
        clearInterval(interval);
        setPollingInterval(null);
      }
    }, 1000);

    setPollingInterval(interval);
  };

  // Clean up intervals on unmount
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [pollingInterval]); // Include intervals in dependency array to ensure cleanup when they change

  // Effect to abort fetch request on navigation/unmount
  useEffect(() => {
    // This will run when the component unmounts
    return () => {
      // Abort any ongoing fetch requests
      abortFetchRequest();
    };
  }, []);

  // Function to abort any ongoing fetch request
  const abortFetchRequest = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  };

  const handleErrorResponse = async (
    response: Response | { status: number; data?: { error: string } | string },
  ) => {
    setGenerating(false);
    setUploadProgress(0);
    setDownloadProgress(0);
    setRequestFullySent(false);

    // Stop polling and notification intervals if they're running
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }

    const status = response.status;
    let errorMessage = "Something went wrong please try again.";

    // Try to get the error message from the response if it's JSON
    try {
      if ("json" in response && typeof response.json === "function") {
        // Handle standard Response object
        const contentType = response.headers.get("Content-Type");
        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          }
        }
      } else if ("data" in response && response.data) {
        // Handle Axios response
        if (typeof response.data === "object" && response.data.error) {
          errorMessage = response.data.error;
        } else if (typeof response.data === "string") {
          try {
            const parsedData = JSON.parse(response.data);
            if (parsedData.error) {
              errorMessage = parsedData.error;
            }
          } catch {
            // If it's not valid JSON, use the string as is
            errorMessage = response.data;
          }
        }
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.error("Error parsing error response:", error.message);
      } else {
        console.error("Error parsing error response:", error);
      }
    }

    const errorMessages = {
      401: {
        title: "Signed out",
        message: "You need to be signed in to generate the image.",
      },
      429: {
        title: "Limit Reached!",
        message:
          "You reached the limit for the free trial, become a PRO to get unlimited generations.",
      },
      413: {
        title: "Failed generating.",
        message:
          "Uploaded images are too large. The preset needs to be less then 64mb",
      },
      500: {
        title: "Failed generating.",
        message: errorMessage,
      },
      0: {
        // Add this for connection refused errors
        title: "Server Offline",
        message: (
          <div>
            <p className={"mb-2"}>
              The server is currently unavailable. You can download the card
              <strong> preset</strong> to save your work and try again later.
            </p>
            <p>
              Please contact <strong><EMAIL></strong> if the
              issue persists, try reloading to see if the server is back online.
            </p>
          </div>
        ),
      },
    };

    const error =
      errorMessages[status as keyof typeof errorMessages] || errorMessages[500];

    showErrorToast(error.title, {
      description: error.message,
    });
  };

  const handleDownload = async () => {
    if (user.user === null) {
      // Create a mock response for auth error
      const mockResponse = new Response(null, { status: 401 });
      await handleErrorResponse(mockResponse);
      return;
    }
    try {
      // Get the session
      const {
        data: { session },
      } = await supabase.auth.getSession();
      const accessToken = session?.access_token;

      posthog.identify(user?.user?.id, { email: user?.user?.email });
      setGenerating(true);
      setUploadProgress(0);
      setDownloadProgress(0);

      // Create an AbortController that works with both fetch and axios
      abortControllerRef.current = new AbortController();
      const signal = abortControllerRef.current.signal;

      const state = {
        ...storeState,
        cardKindRoute: cardKind,
        imageFile: null,
      };

      // Check subscription requirements
      if (
        (state.printReady &&
          !subscription.subscriptionName.includes("Creator") &&
          !(subscription.active && subscription.lifetime)) ||
        (subscription.subscriptionName.includes("Creator") &&
          !subscription.active &&
          !subscription.lifetime)
      ) {
        showErrorToast("Failed generating", {
          description: `You need to subscribe to "Creator" plan to use print ready presets.`,
        });
        setGenerating(false);
        return null;
      }

      // Check card tokens
      if (subscription.status !== "active" && !subscription.lifetime) {
        if ((cardTokens && cardTokens < 5) || !cardTokens) {
          showErrorToast("Failed generating", {
            description: `You reached the limit for the free trial, subscribe to get unlimited generations.`,
          });
          setGenerating(false);
          return null;
        }
        setCardTokens((token) => Number(token) - 1);
      }

      // Get initial queue status and start polling
      const res = await fetchQueueStatus();
      if (res) {
        startPolling();
      }
      // startPolling();

      // Send an initial notification immediately

      // Make a single request to generate the card
      // We'll set startedGenerating after the request is sent and the server starts processing

      // Reset the requestFullySent state before making the request
      setRequestFullySent(false);

      // Use axios instead of fetch to track upload progress

      const formData = new FormData();

      // Convert Base64 to Blob for both images
      if (state.imageUrl) {
        const imageBlob = dataURLToBlob(state.imageUrl);
        formData.append("image", imageBlob, "cropped.png");
      }

      if (state.backgroundImageUrl) {
        const backgroundBlob = dataURLToBlob(state.backgroundImageUrl);
        formData.append("backgroundImage", backgroundBlob, "background.png");
      }

      // Add the rest of the state (without the Base64 images)
      const stateToSend = { ...state };
      stateToSend.imageUrl = "";
      stateToSend.backgroundImageUrl = "";
      formData.append("metadata", JSON.stringify(stateToSend));

      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_GET_CARD_API_URL}/api/get-card`,
        JSON.stringify({
          ...state,
        }),
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
          signal,
          onUploadProgress: (progressEvent) => {
            // Check if the upload is complete
            if (progressEvent.loaded === progressEvent.total) {
              setRequestFullySent(true);
              setServerConnectionError(false);
              setIsDismissed(true);
            }
            // Update upload progress based on percentage
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / Number(progressEvent.total),
            );
            setUploadProgress(percentCompleted);
          },
          onDownloadProgress: (progressEvent) => {
            // Update download progress based on percentage
            if (progressEvent.total) {
              const percentCompleted = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total,
              );
              setDownloadProgress(percentCompleted);
            }
          },
          responseType: "arraybuffer",
        },
      );

      // Check in preview

      // const response = await axios.post(
      //   `${process.env.NEXT_PUBLIC_GET_CARD_API_URL}/api/get-card`,
      //   formData,
      //   {
      //     headers: {
      //       Authorization: `Bearer ${accessToken}`,
      //       // Don't set Content-Type manually — axios will set it to multipart/form-data
      //     },
      //     signal,
      //     onUploadProgress: (progressEvent) => {
      //       // Check if the upload is complete
      //       if (progressEvent.loaded === progressEvent.total) {
      //         setRequestFullySent(true);
      //         setServerConnectionError(false);
      //         setIsDismissed(true);
      //       }
      //       // Update upload progress based on percentage
      //       const percentCompleted = Math.round(
      //         (progressEvent.loaded * 100) / Number(progressEvent.total),
      //       );
      //       setUploadProgress(percentCompleted);
      //     },
      //     onDownloadProgress: (progressEvent) => {
      //       // Update download progress based on percentage
      //       if (progressEvent.total) {
      //         const percentCompleted = Math.round(
      //           (progressEvent.loaded * 100) / progressEvent.total,
      //         );
      //         setDownloadProgress(percentCompleted);
      //       }
      //     },
      //     responseType: "arraybuffer",
      //   },
      // );

      // Now the request has been sent and the server has responded

      // Check for error responses
      if (response.status !== 200) {
        // Create a Response object that handleErrorResponse can work with
        const mockResponse = new Response(JSON.stringify(response.data), {
          status: response.status,
          headers: new Headers({
            "Content-Type": "application/json",
          }),
        });
        await handleErrorResponse(mockResponse);
        return;
      }

      // Set download progress to 100% when download is complete
      setDownloadProgress(100);

      // Create a blob from the response data
      const blob = new Blob([response.data], { type: "image/png" });

      // Blob is already created from the response data
      const url = window.URL.createObjectURL(blob);
      setGenerating(false);
      setUploadProgress(0);
      setDownloadProgress(0);
      handleDownloadClick(url);
      posthog.capture("Successfully Generated Image", {
        cardType: cardKind,
        subscription: subscription?.subscriptionName,
        isSubscribed: subscription?.active,
      });

      // Stop polling and notification intervals after successful download
      if (pollingInterval) {
        clearInterval(pollingInterval);
        setPollingInterval(null);
      }
    } catch (error: unknown) {
      const err = error as CustomError;

      if (err.name === "AbortError" || err.code === "ERR_CANCELED") {
        console.log("Download aborted");
        setGenerating(false);
        setUploadProgress(0);
        setDownloadProgress(0);
        setRequestFullySent(false);

        if (pollingInterval) {
          clearInterval(pollingInterval);
          setPollingInterval(null);
        }

        abortControllerRef.current = null;
      } else {
        console.log("Download error:", err);

        const errorMessage =
          "Failed to generate card image. Please try again <NAME_EMAIL> if the issue persists.";

        if (
          err.code === "ERR_NETWORK" ||
          (err.message && err.message.includes("Network Error"))
        ) {
          err.status = 0;
          await handleErrorResponse({
            ...err,
            status: err.status ?? 0,
          });
          return;
        }

        if (err.response) {
          await handleErrorResponse(err.response);
          return;
        } else {
          const errorResponse = new Response(
            JSON.stringify({
              error: errorMessage,
            }),
            {
              status: 500,
              headers: {
                "Content-Type": "application/json",
              },
            },
          );
          await handleErrorResponse(errorResponse);
        }
      }
    }
  };

  const handleDownloadClick = (url: string) => {
    if (url) {
      // Create a safe filename using card name, type and kind
      const cardName = storeState.name.trim() || "unnamed";
      const cardType = storeState.cardType.trim() || "card";
      const cardKindName = cardKind;

      // Format the filename and make it safe for file systems
      const safeCardName = cardName.replace(/[^a-zA-Z0-9-_]/g, "_");
      const safeCardType = cardType.replace(/[^a-zA-Z0-9-_]/g, "_");
      const filename = `${safeCardName}-${safeCardType}-${cardKindName}.png`;

      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      link.click();

      window.URL.revokeObjectURL(url);
    }
  };

  const getButtonText = () => {
    if (generating) {
      // If there's a queue and we're not at the front
      if (uploadProgress === 100 && queueStatus.size > 0) {
        return `Waiting in queue (Position: ${queueStatus.size})`;
      }
      // If upload is complete (100%) but download hasn't started yet (0%)
      if (uploadProgress === 100 && downloadProgress === 0) {
        // ✅ Stop polling as soon as download starts
        if (pollingInterval) {
          clearInterval(pollingInterval);
          setPollingInterval(null);
        }
        return (
          <p className={"flex items-center gap-2"}>
            <span>Generating image</span>
            <CustomLoader />
          </p>
        );
      }
      // If the request has been fully sent to the server and download is in progress
      if (requestFullySent && downloadProgress > 0) {
        return `Downloading ${downloadProgress}%`;
      }
      // If we're uploading but not yet fully sent
      if (uploadProgress > 0 && !requestFullySent) {
        return `Uploading ${uploadProgress}%`;
      }
      // Initial state when button is clicked and we're waiting for response
    }
    return "Generate Image";
  };

  return (
    <>
      {!isCropping && !isCroppingBackground && (
        <div className="relative w-full rounded-sm bg-neutral-600">
          {/* Progress bar overlay */}
          {generating && (
            <div
              className="bg-ring absolute inset-0 z-0 transition-all duration-300 ease-in-out"
              style={{
                width: `${requestFullySent ? 50 + downloadProgress / 2 : uploadProgress / 2}%`,
                opacity: 1,
                borderRadius: "4px",
              }}
            />
          )}
          <Button
            className="disabled:bg-ring relative z-10 w-full tracking-wide disabled:text-neutral-50 disabled:opacity-100"
            onClick={(e) => {
              e.preventDefault();
              handleDownload();
              posthog.capture("download_button_clicked", {
                cardType: cardKind,
              });
            }}
            disabled={generating}
            style={{
              fontWeight: 500,
              background: generating ? "transparent" : undefined,
            }}
          >
            {getButtonText()}
          </Button>
        </div>
      )}
    </>
  );
}
// function isCustomError(error: unknown): error is CustomError {
//   return (
//     typeof error === "object" &&
//     error !== null &&
//     "name" in error &&
//     typeof (error as any).name === "string"
//   );
// }

interface CustomError extends Error {
  code?: string;
  response?: {
    status: number;
    data?: { error: string };
    // add other properties you expect on response if needed
  };
  status?: number;
}
function dataURLToBlob(dataURL: string): Blob {
  const [header, base64] = dataURL.split(",");
  const mime = header.match(/:(.*?);/)?.[1] || "image/png";
  const binary = atob(base64);
  const array = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    array[i] = binary.charCodeAt(i);
  }
  return new Blob([array], { type: mime });
}
