"use client";
import "@mantine/tiptap/styles.css";

import { TextStyle } from "@tiptap/extension-text-style";
import { Bold } from "@tiptap/extension-bold";
import { useDispatch, useSelector } from "react-redux";
import { RichTextEditor, useRichTextEditorContext } from "@mantine/tiptap";
import React, { useEffect, useRef } from "react";
import {
  onTriggerTextFontSize,
  onTriggerEditorState,
  onTriggerText,
} from "@/store/formSlice";

import { StarterKit } from "@tiptap/starter-kit";
import { Underline } from "@tiptap/extension-underline";

import Highlight from "@tiptap/extension-highlight";
import { TextAlign } from "@tiptap/extension-text-align";
import { Color } from "@tiptap/extension-color";

import { BubbleMenu, SingleCommands, useEditor } from "@tiptap/react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { <PERSON><PERSON>, MantineProvider, Tooltip } from "@mantine/core";
import { storeState } from "@/store/store";

const CustomParagraph = TextStyle.extend({
  renderHTML({ HTMLAttributes }) {
    if (HTMLAttributes.class === "orange-ability") {
      return [
        "span",
        { class: "orange-ability-container" },
        [
          "span",
          { class: "orange-ability-shadow" },
          ["span", { ...HTMLAttributes }, 0],
        ],
      ];
    } else {
      return ["span", { ...HTMLAttributes }, 0];
    }
  },
  addAttributes() {
    return {
      color: {
        // … and customize the HTML rendering.
        renderHTML: (attributes) => {
          const color = attributes.color;
          const classColor =
            color === "#2F77B3"
              ? "blue-ability"
              : color === "#d94880"
                ? "pink-ability"
                : color === "#DC8535"
                  ? "orange-ability"
                  : color === "#ba212f"
                    ? "red-ability"
                    : color === "#f8ed70"
                      ? "trigger-ability"
                      : color === "#FFFFFF"
                        ? "white-ability"
                        : color === "#000000"
                          ? "black-ability"
                          : "";
          return {
            class: classColor,
          };
        },
      },
    };
  },
});

const CustomBold = Bold.extend({
  name: "customBold",
  renderHTML({ HTMLAttributes }) {
    // Original:
    // return ['strong', HTMLAttributes, 0]
    return ["b", HTMLAttributes, 0];
  },
});

export default function TriggerInput() {
  const editorState = useGetStoreState("triggerEditorState");
  const dispatch = useDispatch();

  // const notStageRoute = route.Characters !== "Stage";
  // const notEventRoute = route.Characters !== "Event";
  // const notDonRoute = route.Characters !== "Don";
  const editor = useEditor({
    content: editorState,
    immediatelyRender: false,
    extensions: [
      StarterKit,
      Underline,
      Highlight,
      TextAlign.configure({ types: ["heading", "paragraph"] }),
      Color,
      CustomParagraph,
      CustomBold,
    ],
    editorProps: {
      attributes: {
        class: "ability-input",
      },
    },
    onUpdate({ editor }) {
      dispatch(onTriggerText(editor.getHTML()));
    },
    onDestroy() {
      dispatch(onTriggerEditorState(editor?.getJSON()));
    },
    onBlur() {
      dispatch(onTriggerEditorState(editor?.getJSON()));
    },
    onCreate({ editor }) {
      editor.commands.setContent(editorState);
    },
  });
  return (
    <>
      <MantineProvider
        defaultColorScheme={"auto"}
        theme={{
          components: {
            Button: Button.extend({
              styles: {
                inner: { fontWeight: 500 },
              },
            }),
          },
        }}
      >
        <div className={"flex flex-col"}>
          <label className={"text-sm"}>Trigger</label>
          <p className={"text-xs text-[#868e96]"}>Card trigger</p>
          <RichTextEditor
            editor={editor}
            className={`relative mt-1 min-h-[56px] max-w-full bg-stone-800`}
            // classNames={{
            //   content: "dark:bg-background!",
            //   toolbar: "dark:bg-background!",
            //   control:
            //     "dark:bg-card! dark:hover:bg-full-button-hover! transition-all duration-100!",
            // }}
            id={"ability"}
          >
            <RichTextEditor.Toolbar className={"flex flex-row"}>
              <RichTextEditor.ControlsGroup>
                <TextIncreaseDecrease by={-0.5} label={"Decrease font size"}>
                  {"<"}
                </TextIncreaseDecrease>
                <TextSize />
                <TextIncreaseDecrease by={0.5} label={"Increase font size"}>
                  {">"}
                </TextIncreaseDecrease>
              </RichTextEditor.ControlsGroup>
            </RichTextEditor.Toolbar>

            {editor && (
              <BubbleMenu editor={editor} tippyOptions={{ offset: [80, -60] }}>
                <RichTextEditor.ControlsGroup className={"flex"}>
                  <Tooltip
                    label={"OnPlay"}
                    className={
                      "blue-ability-tooltip z-50 bg-neutral-100 text-neutral-900 dark:text-neutral-100"
                    }
                  >
                    <RichTextEditor.Color color="#2F77B3" />
                  </Tooltip>
                  <Tooltip
                    label={"Once Per Turn"}
                    className={
                      "pink-ability-tooltip z-50 bg-neutral-100 text-neutral-900 dark:text-neutral-100"
                    }
                  >
                    <RichTextEditor.Color color="#d94880" />
                  </Tooltip>
                  <Tooltip
                    label={"Blocker"}
                    className={
                      "orange-ability-tooltip z-50 bg-neutral-100 text-neutral-900 dark:text-neutral-100"
                    }
                  >
                    <RichTextEditor.Color color="#DC8535" />
                  </Tooltip>
                  <Tooltip
                    label={"Counter"}
                    className={
                      "counter-ability-tooltip z-50 bg-neutral-100 text-neutral-900 dark:text-neutral-100"
                    }
                  >
                    <RichTextEditor.Color color="#ba212f" />
                  </Tooltip>
                  <Tooltip
                    label={"Trigger"}
                    className={
                      "trigger-ability-tooltip z-50 bg-neutral-100 text-neutral-900 dark:text-neutral-100"
                    }
                  >
                    <RichTextEditor.Color color="#f8ed70" />
                  </Tooltip>
                  <Tooltip
                    label={"1"}
                    className={
                      "number-ability-tooltip z-50 bg-neutral-100 text-neutral-900 dark:text-neutral-100"
                    }
                  >
                    <RichTextEditor.Color color="#FFFFFF" />
                  </Tooltip>
                  <RichTextEditor.Bold />
                  <RichTextEditor.Italic />
                  <RichTextEditor.Underline />
                  <RichTextEditor.Strikethrough />
                  <RichTextEditor.UnsetColor />
                </RichTextEditor.ControlsGroup>
              </BubbleMenu>
            )}

            <RichTextEditor.Content
              onChange={() => {}}
              className={`font-roboto min-h-[56px] overflow-clip`}
            />
            <DefaultStateSetter />
          </RichTextEditor>
        </div>
      </MantineProvider>
    </>
  );
}

function DefaultStateSetter() {
  // Get the editor instance from the context
  const editor = useRichTextEditorContext();

  // Get the default state for the editor from some store
  const editorDefaultState = useGetStoreState("triggerEditorState");

  const commandsRef = useRef<SingleCommands>(undefined);

  // Update the ref if the editor commands change
  useEffect(() => {
    commandsRef.current = editor?.editor?.commands;
  }, [editor?.editor?.commands]);

  useEffect(() => {
    // Only set content if the commands have not changed
    if (commandsRef.current) {
      commandsRef.current.setContent(editorDefaultState);
    }
  }, [editorDefaultState]);

  // This component does not render anything
  return null;
}

function TextIncreaseDecrease({
  children,
  by,
  label,
}: {
  children: React.ReactNode;
  by: number;
  label: string;
}) {
  const dispatch = useDispatch();
  const triggerTextFontSize = useGetStoreState("triggerTextFontSize");
  return (
    <RichTextEditor.Control
      onClick={() => {
        dispatch(onTriggerTextFontSize(by));
      }}
      aria-label={label}
      title={label}
      className={"px-2!"}
      disabled={triggerTextFontSize === 1 && label === "Decrease font size"}
    >
      {children}
    </RichTextEditor.Control>
  );
}
function TextSize() {
  const size = useSelector(
    (state: storeState) => state.mainFormSlice.triggerTextFontSize,
  );
  const dispatch = useDispatch();
  return (
    <Tooltip label={"Reset font size"}>
      <RichTextEditor.Control
        onClick={() => {
          dispatch(onTriggerTextFontSize("default"));
        }}
        aria-label="Reset font size"
        // title="Reset font size"
        className={""}
      >
        <div
          className={
            "flex min-w-[4rem] content-center justify-center text-center"
          }
        >
          <p>{size + "px"}</p>
        </div>
      </RichTextEditor.Control>
    </Tooltip>
  );
}
