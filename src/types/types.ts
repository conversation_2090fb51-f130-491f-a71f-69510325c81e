// https://developer.paddle.com/api-reference/subscriptions/overview
export type PaddleSubscription = {
	data: {
		id: string;
		status: "active" | "canceled" | "paused" | "past_due" | "trialing";
		customer_id: string;
		next_billed_at: string | null;
		paused_at: string | null;
		canceled_at: string | null;
		discount: null | {
			ends_at: string | null;
			id: string;
			starts_at: string | null;
		};
		current_billing_period: null | {
			starts_at: string;
			ends_at: string;
		};
		billing_cycle: {
			frequency: number;
			interval: string;
		};
		scheduled_change: null | {
			action: string;
			effective_at: string;
			resume_at: string | null;
		};
		management_urls: {
			update_payment_method: string | null;
			cancel: string;
		};
		items: Array<SubscriptionItem>;
		custom_data: object | null;
	};
};

export type SubscriptionItem = {
	product: {
		name: string;
	};
	price: {
		name: string;
	};
};

export type TransactionItem = {
	price: {
		name: string;
	};
};

export type PaddleTransaction = {
	data: {
		id: string;
		status: string;
		created_at: string;
		customer_id: string;
		subscription_id: string;
		items: Array<TransactionItem>;
	};
};

export interface DownloadState {
	printReady: boolean;
	imageUrl: string;
	backgroundImageUrl: string;
	colorArray: any[];
	ability: string;
	name: string;
	donText?: string;
	donPower?: string;
	donFontSize: string;
	cardType: string;
	triggerText: string;
	set: string;
	rarity: string;
	rarity2: string;
	artist: string;
	cardNum: string;
	counterText: string;
	characterBorder: string;
	life: string;
	leaderBorder: string;
	eventBorder?: string;
	cardKindRoute: string;
	color: string;
	color2: string;
	attribute: string;
	cost: string;
	power?: string;
	abilityBackground: string;
	trigger: string;
	counter: string;
	printWave?: string;
	donAbility?: string;
	foilBorder: boolean;
	dropShadow: boolean;
	abilityDropShadow: boolean;
	abilityTextSize: string;
	blackBorder: boolean;
	rainbow: boolean;
	powerBlack: boolean;
	leaderBorderEnabled: boolean;
	aaStar: boolean;
	typeFontSize: string;
	nameFontSize: string;
	triggerTextFontSize: string;
}
