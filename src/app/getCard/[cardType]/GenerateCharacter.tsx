"use client";
import Card<PERSON><PERSON>ground from "@/components/CardElement-components/card-elements/CardBackground";
import CardBorder from "@/components/CardElement-components/card-elements/CardBorder";
import CardCounter from "@/components/CardElement-components/card-elements/CardCounter";
import CardAttribute from "@/components/CardElement-components/card-elements/CardAttribute";
import CardCost from "@/components/CardElement-components/card-elements/CardCost";

import CardPrintWave from "@/components/CardElement-components/card-elements/CardPrintWave";
import CardRarity from "@/components/CardElement-components/card-elements/CardRarity";

import CardName from "@/components/CardElement-components/card-elements/CardName";
import CardKind from "@/components/CardElement-components/card-elements/CardKind";
import CardType from "@/components/CardElement-components/card-elements/CardType";
import CardPower from "@/components/CardElement-components/card-elements/CardPower";

import CardCounterText from "@/components/CardElement-components/card-elements/CardCounterText";
import CardSetAndNum from "@/components/CardElement-components/card-elements/CardSetAndNum";
import CardRarityText from "@/components/CardElement-components/card-elements/CardRarityText";
import CardPrintWaveText from "@/components/CardElement-components/card-elements/CardPrintWaveText";
import CardArtistText from "@/components/CardElement-components/card-elements/CardArtistText";
import CardMadeWith from "@/components/CardElement-components/card-elements/CardMadeWith";
import Card from "@/components/CardElement-components/Card";

import CardAbilityAndTrigger from "@/components/CardElement-components/card-elements/CardAbilityAndTrigger";
import CardColorWheel from "@/components/CardElement-components/card-elements/CardColorWheel";
import React from "react";
import CharacterCardBorderBottom from "@/components/CardElement-components/card-elements/CharacterCardBorderBottom";
import { useGetStoreState } from "@/helpers/useGetStoreState";

export default function GenerateCharacter({
  CMW,
  softwareAcceleration,
  cfWorker,
}: {
  CMW: boolean;
  softwareAcceleration?: boolean;
  cfWorker?: boolean;
}) {
  const characterBorderEnabled = useGetStoreState("leaderBorderEnabled");
  const printReady = useGetStoreState("printReady");
  return (
    <Card maxWPx={printReady ? 3677 : 3357}>
      <CardBackground cardType={"character"} quality={100} png={true} />
      <CardBorder cardType={"character"} quality={100} png={true} />
      <img
        className={
          "card-background-image absolute top-0 z-[-2] h-full w-full object-cover"
        }
        alt={""}
      />
      <img
        className={
          "cardScreenshot absolute top-0 z-3 h-full w-full object-cover"
        }
        alt={""}
      />
      <CharacterCardBorderBottom cardType={"character"} quality={100} />
      {characterBorderEnabled && (
        <CardBorder
          cardType={"character"}
          quality={100}
          png={true}
          zIndex={4}
        />
      )}
      <CardCounter />
      <CardAttribute cardKind={"character"} />
      <CardCost character={true} />
      <CardPrintWave
        quality={100}
        cardType={"character"}
        cardKindRoute={"character"}
      />
      <CardRarity
        quality={100}
        cardType={"character"}
        cardKindRoute={"character"}
      />
      {/*<CardArtist quality={100} cardType={"character"} />*/}
      <CardAbilityAndTrigger triggerQuality={100} cardType={"character"} />
      <CardName cardType={"character"} />
      <CardKind cardType={"character"} />
      <CardType cardType={"character"} />
      <CardPower cardType={"character"} />
      <CardCounterText />
      <CardSetAndNum cardType={"character"} />
      <CardRarityText
        cardType={"character"}
        softwareAcceleration={softwareAcceleration}
        cfWorker={cfWorker}
      />
      <CardPrintWaveText cardType={"character"} />
      <CardArtistText cardType={"character"} />
      {CMW && <CardMadeWith cardType={"character"} />}
      <CardColorWheel cardKind={"character"} png={true} />
    </Card>
  );
}
