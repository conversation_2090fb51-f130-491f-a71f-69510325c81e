.ability-text {
    font-weight: 500;
    letter-spacing: -0.018em;
}
.ability-text p {
    padding-right: 0.09em !important;
}
/* Bullet list styling for ability-text */
.ability-text ul, .tiptap ul , .trigger-text ul {
    list-style-type: none;
    padding: 0 !important;
    margin-bottom: 0 !important;
    /*padding-left: 1.2em;*/

}

.ability-text ul li, .tiptap ul li, .trigger-text ul li {
    position: relative;

}

.ability-text ul li p, .tiptap ul li p, .trigger-text ul li p {
    display: inline-block;
    position: relative;
    z-index: 1;
    margin: 0;
    padding: 0;
}


.ability-text ul li::before, .tiptap ul li::before , .trigger-text ul li::before{
    content: "∙";
    position: relative;
    /*top: 0.1em;*/
    font-size: 0.75em;
    bottom: 0.13em;
    /*line-height: 1.25em;*/
    /*padding: 0.1em 0.1em 0.1em 0.1em;*/

    color: currentColor;
    z-index: 0;
}

.no-ability-background .ability-text ul li::before{
    background-image: linear-gradient(to right, #FFFFFF,  #FFFFFF);
    background-clip: text;
    -webkit-text-stroke-color: transparent;
    -webkit-text-stroke-width: calc(3.5em / 16);
    left: 0.1em;
    /*padding: 0.1em 0.1em 0.1em 0.1em;*/
    -webkit-background-clip: text;
}

/*!* Ensure bullet points are visible in dark mode *!*/
/*.dark .ability-text ul li::before {*/
/*    color: currentColor;*/
/*}*/

/*.tiptap {*/
/*    !*:first-child {*!*/
/*    !*    margin-top: 0;*!*/
/*    !*}*!*/

/*    !* List styles *!*/
/*    ul,*/
/*    ol {*/
/*        padding: 0 1rem;*/
/*        margin: 1.25rem 1rem 1.25rem 0.4rem;*/

/*        li p {*/
/*            margin-top: 0.25em;*/
/*            margin-bottom: 0.25em;*/
/*        }*/
/*    }*/
/*}*/
.blue-ability, .mantine-Modal-content .blue-ability-tooltip ,[style="color: rgb(47, 119, 179)"]{
    font-weight: 400;
    position: relative;
    display: inline-block;
    padding: 0 0.3em  1.2em 0.35em;
    border-radius: 0.3em;
    height: 1.15em;
    background-color: #2F77B3;
    color: #fff !important;
    letter-spacing: 0.02em;
    font-size: 0.827em;
    line-height: 1.10em;

    text-shadow: 0 0 0;
    vertical-align: text-top;
    margin-top: 0.1em;

}
.ability-text-screen-shoot .blue-ability , .ability-text-screen-shoot .pink-ability, .ability-text-screen-shoot .red-ability, .ability-text-screen-shoot .orange-ability, .ability-text-screen-shoot .trigger-ability,.ability-text-screen-shoot .white-ability,.ability-text-screen-shoot .black-ability {
    line-height: 0 !important
;
}

.red-ability, .mantine-Modal-content .counter-ability-tooltip{
    font-weight: 300;
    position: relative;
    display: inline-block;

    border-radius: 0.3em;
    height: 1.15em;
    z-index: 1;
    background-color: #ba212f;
    color: white !important;
    letter-spacing: 0.02em;
    font-size: 0.85em;
    line-height: 1.1em;
    text-shadow: 0 0 0;
    padding-left: 0.94375em;
    padding-right: 0.3em;

    vertical-align: text-top;
    margin-top: 0.1em;
}
.red-ability::before, .mantine-Modal-content .counter-ability-tooltip::before{
    content: "\200B";
    display: inline-block;
    position: absolute;
    left: 0.17em;
    top: 0.1em;
    width: 0.64375em;
    z-index: 9999;
    height: 0.925em;
    margin-right: 0.15em;
    background-image: url('https://r2.ultimatetcgcm.com/assets/CounterYellow.png');
    background-size: cover;  /* Or any other value as per your need */
    background-position: center;

}

.pink-ability, .mantine-Modal-content .pink-ability-tooltip {

    font-weight: 400;
    position: relative;
    display: inline-block;
    padding: 0 0.25em 0 0.25em;
    border-radius: 1em;
    height: 1.25em;
    background-color: #d94880;
    color: white !important;
    letter-spacing: 0.04em;
    font-size: 0.827em;
    line-height: 1.2em;
    text-shadow: 0 0 0;
    vertical-align: text-top;
    margin-top: 0.13em;

}
.tiptap .pink-ability{
    margin-top: 0;
}

.black-ability, .mantine-Modal-content .don-ability-tooltip{

    font-weight: 400;
    position: relative;
    display: inline-block;
    padding: 0 0.3em 0;

    border-top: 0 solid transparent;
    border-right: 0.2em solid #000;

    height: 1.15em;
    background-color: #000;
    color: white !important;
    letter-spacing: 0.04em;
    font-size: 0.867em;
    line-height: 1.15em;
    text-shadow: 0 0 0;
    vertical-align: text-top;
    margin-top: 0.08em;
    clip-path: polygon(50% 0%, 6% 0%, 0% 25%, 0% 75%, 6% 100%, 94% 100%, 100% 75%, 100% 25%, 94% 0%, 50% 0%);
}

.orange-ability{
    margin-left: 0.65em;
    margin-right: 0.65em;
    /*margin-left: 0.45em;*/
    font-weight: 300;
    position: relative;
    display: inline-block;
    /*padding: 0 0.3em 0;*/
    z-index: 5;
    border-top: 0 solid transparent;

    height: 1.165em;
    background-color: #DC8535;
    color: white !important;
    letter-spacing: 0.02em;
    font-size: 0.867em;
    line-height: 1.15em;
    text-shadow: 0 0 0;
    vertical-align: text-top;
    margin-top: 0.08em;
    --width: 100%;
    --height: 100%;
    /*clip-path: polygon(50% 0%, 5em 6em, 0em 0.6em, 0.5em 0em, 0.5em 0em);*/
    /*clip-path: polygon(50% 0%, 0.62em 0%, 0% 50%, 0% 50%, 0.62em 100%, 4.22em 100%, 100% 50%, 100% 50%, 87% 0%, 50% 0%);*/
    /*clip-path: polygon(50% 0%, 13% 0%, 0% 50%, 0% 50%, 13% 100%, 87% 100%, 100% 50%, 100% 50%, 87% 0%, 50% 0%);*/
    /*    clip-path: polygon(
                calc(var(--width) * 0.5) 0,
                calc(var(--width) * 0.13) 0,
                0 calc(var(--height) * 0.5),
                0 calc(var(--height) * 0.5),
                calc(var(--width) * 0.13) var(--height),
                calc(var(--width) * 0.87) var(--height),
                var(--width) calc(var(--height) * 0.5),
                var(--width) calc(var(--height) * 0.5),
                calc(var(--width) * 0.87) 0,
                calc(var(--width) * 0.5) 0
        );*/
}
/*.no-colored-ability-background .orange-ability-shadow{

    display: inline-block;
    outline: 0.623em solid white; !* Second border *!
    outline-offset: -0.4em; !* Space between border and outline *!
    height: 1.21em !important;
    width: 100% !important;
    padding: 0.04em 0.11em 0 0.11em;
    clip-path: polygon(50% 0%, 6.5% 0%, 0% 24%, 0% 76%, 6.5% 100%, 93.5% 100%, 100% 76%, 100% 24%, 93.5% 0%, 50% 0%);
    z-index: -1;!*ust the color and size as needed *!
    font-size: 1.037em;
    line-height: 1.15em;

    vertical-align: top;
    margin-top: 0.08em;
}
*/
.orange-ability-shadow {

    display: inline-block;

}
.orange-ability-container{
    position: relative;
    display: inline-block;
}

.orange-ability-container::after {
    content: "";
    position: absolute;

    height: 1.005em;
    width: 1.01em;
    z-index: 1;
    top: 0.195em;
    right: 0.04em;
    background: #DC8535;
    clip-path: polygon(0% 0%, 0% 0%, 0% 50%, 0% 100%, 13% 100%, 50% 100%, 100% 50%, 100% 50%, 50% 0%, 50% 0%);
}
.ability-input .orange-ability-container::after {
    top: 0.175em;
}
.ability-input .orange-ability-container::before {
    top: 0.175em;
}
.trigger-text .orange-ability-container::after, .trigger-text .orange-ability-container::before {
    top: 0.265em;
}
.no-colored-ability-background .orange-ability-container::after, .no-colored-ability-background .orange-ability-container::before {
    top: 0.185em;
    height: .995em;
}
.no-colored-ability-background .orange-ability-container::after{
    right: 0.2em;
}
.no-colored-ability-background .orange-ability-container::before{
    left: 0.2em;
}
.no-colored-ability-background .orange-ability-shadow::after {
    content: "";
    position: absolute;

    height: 1.245em;
    width: 1.01em;
    z-index: 0;
    top: 0.06em;
    right: 0.04em;
    background: #fff;
    clip-path: polygon(0% 0%, 0% 0%, 0% 50%, 0% 100%, 13% 100%, 40% 100%, 100% 50%, 100% 50%, 40% 0%, 50% 0%);
}
.orange-ability-container::before {
    content: "";
    position: absolute;

    height: 1.005em;
    width: 1.01em;
    z-index: 2;
    top: 0.195em;
    left: 0.04em;
    background: #DC8535;
    clip-path: polygon(0% 50%, 0% 50%,  50% 100%, 50% 100%, 100% 50%, 50% 100%, 100% 100%, 100% 0%, 50% 0%, 50% 0%);
}
.no-colored-ability-background .orange-ability-shadow::before {
    content: "";
    position: absolute;

    height: 1.245em;
    width: 1.01em;
    z-index: 0;
    top: 0.06em;
    left: 0.04em;
    background: #fff;
    clip-path: polygon(0% 50%, 0% 50%,  60% 100%, 50% 100%, 100% 50%, 50% 100%, 100% 100%, 100% 0%, 50% 0%, 60% 0%);
}
.no-colored-ability-background .orange-ability{
    border-top:  solid #fff 0.15em;
    border-bottom: solid #fff 0.15em;
    height: 1.5em !important;
    font-size: 0.827em;
    vertical-align: top;
    margin-left: 0.90em;
    margin-right: 0.90em;
}
/*CONTINUE HERE*/
.white-ability {
    font-weight: 600;
    position: relative;
    display: inline-block;
    padding: 0 0.55em ;
    border: solid 0.08em;
    border-radius: 1em;
    background-image: linear-gradient(to right, #FFFFFF,  #FFFFFF);
    background-clip: text;
    -webkit-text-stroke-color: transparent;
    -webkit-text-stroke-width: calc(3em / 16);
    outline: 0.123em solid white; /* Second border */
    outline-offset: -0.2em; /* Space between border and outline */
    box-shadow: 0 0 0 0.113em white;
    -webkit-background-clip: text;
    color: #1c1917 !important;
    letter-spacing: 0.04em;
    font-size: 0.727em;
    text-shadow: 0 0 0;
    vertical-align: text-top;
    margin-top: 0.2em;

}

.white-ability, .mantine-Modal-content .number-ability-tooltip{
    font-weight: 600;
    position: relative;
    top: 0.01em;
    display: inline-block;
    padding: 0 0.29em ;
    border: solid 0.08em;
    border-radius: 10em;
    height: 1.25em;
    color: #1c1917 !important;
    letter-spacing: 0;
    line-height: 1.15em;
    font-size: 0.727em;
    text-shadow: 0 0 0;
    min-width: 1.3em;
    text-align: center;
    background-image: linear-gradient(to right, #FFFFFF,  #FFFFFF);

}
.no-ability-background .white-ability {
    background-image: linear-gradient(to right, #FFFFFF,  #FFFFFF);
    background-clip: text;
    -webkit-text-stroke-color: transparent;
    -webkit-text-stroke-width: calc(3em / 16);

    -webkit-background-clip: text;
}

.no-colored-ability-background .blue-ability, .no-colored-ability-background .pink-ability,.no-colored-ability-background .red-ability {
    border: solid #fff 0.15em;
    height: 1.5em !important;
    font-size: 0.827em;
    vertical-align: top;

}
.no-colored-ability-background .black-ability-shadow{

    display: inline-block;
    outline: 0.623em solid white; /* Second border */
    outline-offset: -0.4em; /* Space between border and outline */
    height: 1.21em !important;
    width: 100% !important;
    padding: 0.04em 0.11em 0 0.11em;
    clip-path: polygon(50% 0%, 6.5% 0%, 0% 24%, 0% 76%, 6.5% 100%, 93.5% 100%, 100% 76%, 100% 24%, 93.5% 0%, 50% 0%);
    z-index: -1;/*ust the color and size as needed */
    font-size: 1.037em;
    line-height: 1.15em;

    vertical-align: top;
    margin-top: 0.08em;
}

.no-colored-ability-background .black-ability-container{

    display: inline-block;
}

.no-colored-ability-background .blue-ability{

    border-radius: 0.4em;

}
.no-colored-ability-background .pink-ability{
    border: solid #fff 0.14em;

}
/*.no-colored-ability-background .orange-ability {
    border-top: solid #fff 0.11em;
    border-bottom: solid #fff 0.11em;
    border-left: solid #fff 0.11em;
    border-right: solid #fff 0.11em;
    height: 1.25em !important;
}*/
.no-colored-ability-background [data-color="#DC8535"]{
    border-top: solid #fff 0.08em;
    border-bottom: solid #fff 0.08em;
    height: 1.25em !important;
}

/*.orange-ability-container {
    position: relative;
}*/

/*.orange-ability{
    font-weight: 400;
    position: relative;
    display: inline-block;
    padding: 0 0.04em 0;
    margin-left: 0.7em;
    margin-right: 0.7em;
    background-color: #DC8535;
    color: white !important;
    letter-spacing: 0.00em;
    font-size: 0.867em;
    text-shadow: 0 0 0;
    z-index: 90;

    vertical-align: text-top;
    margin-top: 0.18em;
    height: 1.1em;
    line-height: 1.1em;

}*/

/*.get-card-screenshot .orange-ability::before{
    content: '';
    position: absolute;
    left: -0.887em;
    z-index: 1000;
    top: 0.275em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.56em solid transparent; !* Adjust the size as needed *!
    border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.56em solid #DC8535; !* Adjust the size and color as needed *!
    transform: rotate(90deg);
}*/
/*.get-card-screenshot .orange-ability-container .orange-ability::before{
    top: 0.275em;
    left: -0.817em;

}.get-card-screenshot .orange-ability-container .orange-ability::after{
    top: 0.275em;
     right: -0.817em;

}*/
/*.get-card-screenshot .orange-ability::after{
    content: '';
    position: absolute;
    right: -0.889em;
    z-index: 1000;
    top: 0.275em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.56em solid transparent; !* Adjust the size as needed *!
    border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.56em solid #DC8535; !* Adjust the size and color as needed *!
    transform: rotate(-90deg);
}*/

/*.get-card-screenshot .no-colored-ability-background  .orange-ability-shadow::after{
    content: '';
    position: absolute;
    right: -0.21em;
    top: 0.42em;
    width: 0;
    height: 0;
    z-index: 1;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.555em solid transparent; !* Adjust the size as needed *!
    border-right: 0.555em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.555em solid white; !* Adjust the size and color as needed *!
    transform: rotate(-90deg);
}
.get-card-screenshot .no-colored-ability-background .orange-ability-shadow::before{
    content: '';
    position: absolute;
    left: -0.21em;
    top: 0.42em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.555em solid transparent; !* Adjust the size as needed *!
    border-right: 0.555em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.555em solid white; !* Adjust the size and color as needed *!
    transform: rotate(90deg);
}*/

/*.get-card-screenshot .no-colored-ability-background .orange-ability::after{
    content: '';
    position: absolute;
    right: -0.759em;
    z-index: 1000;
    top: 0.265em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.52em solid transparent; !* Adjust the size as needed *!
    border-right: 0.52em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.52em solid #DC8535; !* Adjust the size and color as needed *!
    transform: rotate(-90deg);
}

.get-card-screenshot .no-colored-ability-background .orange-ability::before{
    content: '';
    position: absolute;
    left: -0.759em;
    z-index: 1000;
    top: 0.265em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.52em solid transparent; !* Adjust the size as needed *!
    border-right: 0.52em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.52em solid #DC8535; !* Adjust the size and color as needed *!
    transform: rotate(90deg);
}*/

.mantine-Modal-content .trigger-ability-tooltip{
    font-weight: 500;
    position: relative;
    display: inline-block;

    color: #1c1917 !important;

    text-shadow: 0 0 0;
    z-index: 100;
    border-radius: 0.4em 0 0 0.4em;

    padding: 0.2em 1.1em 1.2em 0.3em;
    letter-spacing: 0.0em;
    font-size: 0.81em;
    height: 2.05em;
    line-height: 1.7em;
    vertical-align: top;
    margin-top: -0.32em;
    margin-left: 0.1em;

    overflow: clip;
}

.ability-text .trigger-ability {

    /*    outline: 0.623em solid white; !* Second border *!
        outline-offset: -0.4em; !* Space between border and outline *!*/

    font-weight: 300;

    position: relative;
    display: inline-block;
    padding-left: 0.29em;

    border-top: 0 solid transparent;

    /*border-left: 0.13em solid #FDEA42;*/

    border-radius: 0.35em 0 0 0.35em;
    height: 1.87em;
    background-color: #FDEA42;
    color: #1c1917 !important;
    letter-spacing: -0.01em;
    font-size: 0.91em;
    line-height: 1.88em;
    text-shadow: 0 0 0;
    vertical-align: text-top;
    margin-top: -0.35em;
    margin-right: 0.3em;
    clip-path: polygon(0% 0%, 92.5% 0%, 64.5% 100%, 0% 100%);
    width: 126.2% !important;
}
.ability-text .trigger-ability-shadow {

    display: inline-block;

}

.no-colored-ability-background .ability-text .trigger-ability {

    top: 0.13em;
    left: -0.26em;
    width: 93.9% !important;
    height: 1.8em !important;
    clip-path: polygon(0% 0%, 98% 0%, 71% 100%, 0% 100%);
    margin-left: 0.4em;
}
.no-colored-ability-background .ability-text .trigger-ability-shadow {

    display: inline-block;

    /*    height: 1.87em !important;
        width: 100% !important;*/

    clip-path: polygon(0% 0%, 100% 0%, 71% 100%, 0% 100%);
    /*    z-index: -1;!*ust the color and size as needed *!
        font-size: 1.037em;
        line-height: 1.15em;*/

    margin-top: -0.44em;

    background-color: #fff;
    color: #1c1917 !important;
    letter-spacing: -0.01em;
    border-radius: 0.35em 0 0 0.35em;
    line-height: 1.88em;
    text-shadow: 0 0 0;
    vertical-align: text-top;
    width: 116.2% !important;
    overflow: clip;
}

.no-colored-ability-background .trigger-ability-container{

    display: inline-block;
}

/*.black-ability, .mantine-Modal-content .don-ability-tooltip{

    font-weight: 400;
    position: relative;
    display: inline-block;
    padding: 0 0.3em 0;

    border-top: 0em solid transparent;
    border-right: 0.2em solid #000;

    height: 1.15em;
    background-color: #000;
    color: white !important;
    letter-spacing: 0.04em;
    font-size: 0.867em;
    line-height: 1.15em;
    text-shadow: 0 0 0;
    vertical-align: text-top;
    margin-top: 0.08em;
    clip-path: polygon(50% 0%, 6% 0%, 0% 25%, 0% 75%, 6% 100%, 94% 100%, 100% 75%, 100% 25%, 94% 0%, 50% 0%);
}*/

/*.no-ability-background .ability-text .trigger-ability::after {
    outline: 0.123em solid white; !* Second border *!
    outline-offset: -0.2em; !* Space between border and outline *!
}*/
.mantine-Modal-content .trigger-ability-tooltip::after{
    box-sizing: border-box;
    content: '';
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-top: 0 solid transparent;
    border-bottom: 330em solid transparent;
    border-left: 240.8em solid #FDEA42;
    z-index: -1;
    position: absolute;
}
.trigger-text  > p > p:first-child {
    margin-left: 3.75em;

}
.trigger-text  > p:first-child {
    margin-left: 3.75em;

}

.trigger-text .trigger-ability, .ability-input .trigger-ability{
    font-weight: 500;
    position: relative;
    display: inline-block;
    padding: 0.2em 1.1em 1.2em 0.3em;
    color: #1c1917 !important;
    letter-spacing: 0.0em;
    font-size: 0.51em;
    text-shadow: 0 0 0;
    z-index: 10;
    border-radius: 0.4em 0 0 0.4em;
    bottom: 0.1em;

    height: 2.05em;
    line-height: 1.7em;
    overflow: clip;
    vertical-align: text-top;
    margin-top: 0.3em;
    margin-left: 0.25em;
}

.trigger-text .trigger-ability::after, .ability-input .trigger-ability::after{
    box-sizing: border-box;
    content: '';
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-top: 0 solid transparent;
    border-bottom: 330em solid transparent;
    border-left: 240.8em solid #FDEA42;
    z-index: -1;
    position: absolute;
}

.attribute-img {
    position: absolute;
    right: 1.3rem;
    top: 3px;
    height: 4.2rem;
    width: auto;
}

#card {
    background-image: url('http://localhost:2297/triangleLeft4BIG.png');
}



/*.orange-ability-container .orange-ability::before{
    content: '';
    position: absolute;
     left: -0.727em;
     z-index: 1000;
    top: 0.285em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.56em solid transparent; !* Adjust the size as needed *!
    border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.56em solid #DC8535; !* Adjust the size and color as needed *!
    transform: rotate(90deg);
}
.orange-ability::before{
    content: '';
    position: absolute;
    left: -0.807em;
    z-index: 1000;
    top: 0.285em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.56em solid transparent; !* Adjust the size as needed *!
    border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.56em solid #DC8535; !* Adjust the size and color as needed *!
    transform: rotate(90deg);
}*/
/*.get-card-screenshot .trigger-text .orange-ability::before{
    content: '';
    position: absolute;
    left: -0.817em;
    z-index: 1000;
    top: 0.285em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.56em solid transparent; !* Adjust the size as needed *!
    border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.56em solid #DC8535; !* Adjust the size and color as needed *!
    transform: rotate(90deg);
}
.trigger-text .orange-ability::before{
    content: '';
    position: absolute;
    left: -0.727em;
    z-index: 1000;
    top: 0.285em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.56em solid transparent; !* Adjust the size as needed *!
    border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.56em solid #DC8535; !* Adjust the size and color as needed *!
    transform: rotate(90deg);
}

.orange-ability-container .orange-ability::after{
    content: '';
    position: absolute;
    right: -0.721em;
    z-index: 1000;
    top: 0.285em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.56em solid transparent; !* Adjust the size as needed *!
    border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.56em solid #DC8535; !* Adjust the size and color as needed *!
    transform: rotate(-90deg);
}
.orange-ability::after{
    content: '';
    position: absolute;
    right: -0.809em;
    z-index: 1000;
    top: 0.285em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.56em solid transparent; !* Adjust the size as needed *!
    border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.56em solid #DC8535; !* Adjust the size and color as needed *!
    transform: rotate(-90deg);
}
.get-card-screenshot .trigger-text .orange-ability::after{
    content: '';
    position: absolute;
    right: -0.819em;
    z-index: 1000;
    top: 0.285em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.56em solid transparent; !* Adjust the size as needed *!
    border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.56em solid #DC8535; !* Adjust the size and color as needed *!
    transform: rotate(-90deg);
}
.trigger-text .orange-ability::after{
    content: '';
    position: absolute;
    right: -0.76em;
    z-index: 1000;
    top: 0.285em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.56em solid transparent; !* Adjust the size as needed *!
    border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.56em solid #DC8535; !* Adjust the size and color as needed *!
    transform: rotate(-90deg);
}*/
/* .no-colored-ability-background  .orange-ability-shadow::after{
    content: '';
    position: absolute;
    right: -0.41em;
    top: 0.3em;
    width: 0;
    height: 0;
    z-index: 1;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.625em solid transparent; !* Adjust the size as needed *!
    border-right: 0.625em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.625em solid white; !* Adjust the size and color as needed *!
    transform: rotate(-90deg);
}
 .no-colored-ability-background .orange-ability-shadow::before{
    content: '';
    position: absolute;
    left: -0.45em;
    top: 0.3em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.625em solid transparent; !* Adjust the size as needed *!
    border-right: 0.625em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.625em solid white; !* Adjust the size and color as needed *!
    transform: rotate(90deg);
}*/

/* .no-colored-ability-background .orange-ability::after{
    content: '';
    position: absolute;
    right: -0.739em;
    z-index: 1000;
    top: 0.285em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.56em solid transparent; !* Adjust the size as needed *!
    border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.56em solid #DC8535; !* Adjust the size and color as needed *!
    transform: rotate(-90deg);
}

 .no-colored-ability-background .orange-ability::before{
    content: '';
    position: absolute;
    left: -0.737em;
    z-index: 1000;
    top: 0.285em;
    width: 0;
    height: 0;
    display: inline-block; !* Add this line to make it a block-level element *!
    border-left: 0.56em solid transparent; !* Adjust the size as needed *!
    border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
    border-top:  0.56em solid #DC8535; !* Adjust the size and color as needed *!
    transform: rotate(90deg);
}*/
/*@media (min-width: 1535px) {
    .no-colored-ability-background .orange-ability::before{
        content: '';
        position: absolute;
        left: -0.737em;
        z-index: 1000;
        top: 0.285em;
        width: 0;
        height: 0;
        display: inline-block; !* Add this line to make it a block-level element *!
        border-left: 0.56em solid transparent; !* Adjust the size as needed *!
        border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
        border-top:  0.56em solid #DC8535; !* Adjust the size and color as needed *!
        transform: rotate(90deg);
    }
    .no-colored-ability-background .orange-ability-shadow::before{
        content: '';
        position: absolute;
        left: -0.16em;
        top: 0.4em;
        width: 0;
        height: 0;
        display: inline-block; !* Add this line to make it a block-level element *!
        border-left: 0.56em solid transparent; !* Adjust the size as needed *!
        border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
        border-top:  0.56em solid white; !* Adjust the size and color as needed *!
        transform: rotate(90deg);
    }

    .no-colored-ability-background .orange-ability::after{
        content: '';
        position: absolute;
        right: -0.739em;
        z-index: 1000;
        top: 0.285em;
        width: 0;
        height: 0;
        display: inline-block; !* Add this line to make it a block-level element *!
        border-left: 0.56em solid transparent; !* Adjust the size as needed *!
        border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
        border-top:  0.56em solid #DC8535; !* Adjust the size and color as needed *!
        transform: rotate(-90deg);
    }
    .no-colored-ability-background  .orange-ability-shadow::after{
        content: '';
        position: absolute;
        right: -0.16em;
        top: 0.4em;
        width: 0;
        height: 0;
        z-index: 1;
        display: inline-block; !* Add this line to make it a block-level element *!
        border-left: 0.56em solid transparent; !* Adjust the size as needed *!
        border-right: 0.56em  solid transparent; !* Adjust the size as needed *!
        border-top:  0.56em solid white; !* Adjust the size and color as needed *!
        transform: rotate(-90deg);
    }
}*/

[role="tooltip"].blue-ability-tooltip {
    font-weight: 400;
    padding: 0 0.3em 0;
    border-radius: 0.4em;
    height: 1.15em;
    background-color: #2F77B3;
    color: white !important;
    letter-spacing: 0.04em;
    font-size: 1em;
    line-height: 1.1em;
    text-shadow: 0 0 0;

}
[role="tooltip"].pink-ability-tooltip {

    font-weight: 400;
    padding: 0 0.3em 0;
    border-radius: 1em;
    height: 1.15em;
    background-color: #d94880;
    color: white !important;
    letter-spacing: 0.04em;
    font-size: 1em;
    line-height: 1.1em;
    text-shadow: 0 0 0;

}

[role="tooltip"].orange-ability-tooltip{
    font-weight: 400;
    padding: 0 0.08em 0;
    background-color: #DC8535;
    color: white !important;
    letter-spacing: 0.04em;
    font-size: 1em;
    text-shadow: 0 0 0;
    z-index: 999;
    border-left: solid #DC8535 0.08em;
    border-right: solid #DC8535 0.08em;

    height: 1.1em;
    line-height: 1.1em;

}

.mantine-Modal-content .orange-ability-tooltip{
    font-weight: 400;
    position: relative;
    display: inline-block;
    padding: 0 0.08em 0;
    margin-left: 0.48em;
    margin-right: 0.48em;
    background-color: #DC8535;
    color: white !important;
    letter-spacing: 0.04em;
    font-size: 1em;
    text-shadow: 0 0 0;
    z-index: 999;
    border-left: solid #DC8535 0.08em;
    border-right: solid #DC8535 0.08em;

    height: 1.1em;
    line-height: 1.1em;

}
[role="tooltip"].orange-ability-tooltip::before{
    content: '';
    position: absolute;
    left: -0.807em;
    z-index: 1000;
    top: 0.275em;
    width: 0;
    height: 0;
    display: inline-block; /* Add this line to make it a block-level element */
    border-left: 0.56em solid transparent; /* Adjust the size as needed */
    border-right: 0.56em  solid transparent; /* Adjust the size as needed */
    border-top:  0.56em solid #DC8535; /* Adjust the size and color as needed */
    transform: rotate(90deg);
}

.mantine-Modal-content .orange-ability-tooltip::before{
    content: '';
    position: absolute;
    left: -0.867em;
    z-index: 1000;
    top: 0.285em;
    width: 0;
    height: 0;
    display: inline-block; /* Add this line to make it a block-level element */
    border-left: 0.57em solid transparent; /* Adjust the size as needed */
    border-right: 0.57em  solid transparent; /* Adjust the size as needed */
    border-top:  0.57em solid #DC8535; /* Adjust the size and color as needed */
    transform: rotate(90deg);
}

[role="tooltip"].orange-ability-tooltip::after{
    content: '';
    position: absolute;
    right: -0.759em;
    z-index: 1000;
    top: 0.275em;
    width: 0;
    height: 0;
    display: inline-block; /* Add this line to make it a block-level element */
    border-left: 0.56em solid transparent; /* Adjust the size as needed */
    border-right: 0.56em  solid transparent; /* Adjust the size as needed */
    border-top:  0.56em solid #DC8535; /* Adjust the size and color as needed */
    transform: rotate(-90deg);
}

.mantine-Modal-content .orange-ability-tooltip::after{
    content: '';
    position: absolute;
    right: -0.879em;
    z-index: 1000;
    top: 0.285em;
    width: 0;
    height: 0;
    display: inline-block; /* Add this line to make it a block-level element */
    border-left: 0.57em solid transparent; /* Adjust the size as needed */
    border-right: 0.57em  solid transparent; /* Adjust the size as needed */
    border-top:  0.57em solid #DC8535; /* Adjust the size and color as needed */
    transform: rotate(-90deg);
}
[role="tooltip"].trigger-ability-tooltip{
    font-weight: 500;
    padding: 0.2em 1.1em 1.2em 0.3em;
    color: #1c1917 !important;
    letter-spacing: 0.04em;
    font-size: 1em;
    text-shadow: 0 0 0;
    z-index: 40;
    border-radius: 0.4em 0 0 0.4em;
    background-color: rgb(0,0,0,0);

    height: 1.15em;
    line-height: 1.1em;
    overflow: clip;
}

[role="tooltip"].trigger-ability-tooltip::after{
    box-sizing: border-box;
    content: '';
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-top: 0 solid transparent;
    border-bottom: 330em solid transparent;
    border-left: 240.8em solid #FDEA42;
    z-index: -1;
    position: absolute;
}

[role="tooltip"].counter-ability-tooltip{
    font-weight: 400;

    border-radius: 0.3em;
    height: 1.15em;
    background-color: #ba212f;
    color: white !important;
    letter-spacing: 0.04em;
    font-size: 1em;
    line-height: 1.1em;
    text-shadow: 0 0 0;
    padding: 0 0.3em 0 1.04375em;

}

[role="tooltip"].counter-ability-tooltip:before{
    content: "\200B";
    display: inline-block;
    position: absolute;
    left: 0.2em;
    top: 0.1em;
    width: 0.64375em;
    z-index: 9999;
    height: 0.925em;
    margin-right: 0.15em;
    background-image: url('https://r2.ultimatetcgcm.com/assets/CounterYellow.png');
    background-size: cover;  /* Or any other value as per your need */
    background-position: center;

}
[role="tooltip"].number-ability-tooltip {
    font-weight: 500;
    top: 0.01em;
    padding: 0 0.38em 0;
    border: solid 0.01em;
    border-radius: 10em;
    height: 1.25em;
    color: #1c1917 !important;
    letter-spacing: 0;
    line-height: 1.15em;
    font-size: 1em;
    text-shadow: 0 0 0;
    background-color: #fff;
}
[role="tooltip"].don-ability-tooltip {
    font-weight: 400;
    padding: 0 0.3em 0;
    border-radius: 0.4em;
    height: 1.15em;
    background-color: #000;
    color: white !important;
    letter-spacing: 0.04em;
    font-size: 1em;
    line-height: 1.1em;
    text-shadow: 0 0 0;

}

#ability-text p {
    padding-left: 0.09em;
    background-image: linear-gradient(to right, #FFFFFF,  #FFFFFF);
    background-clip: text;
    -webkit-text-stroke-color: transparent;
    -webkit-text-stroke-width: calc(3em / 16);
    /*-webkit-text-stroke-width: calc(4.5em / 16);*/
    -webkit-background-clip: text;
    letter-spacing: -0.018em;
}
.text-outline-xxs {
    -webkit-text-stroke-width: calc(0.5em / 16);
}

.text-outline-xs {
    -webkit-text-stroke-width: calc(1.5em / 16);
}

.text-outline-small {
    -webkit-text-stroke-width: calc(2em / 16);
}
.text-outline-standard {
    -webkit-text-stroke-width: calc(2.2em / 16);
}

.text-outline-ms {
    -webkit-text-stroke-width: calc(2.5em / 16);
}
.text-outline {
    padding-left: 0.09em;
    padding-right: 0.09em;
    background-clip: text;
    -webkit-text-stroke-color: transparent;
    -webkit-background-clip: text;
}
.text-outline-large {
    -webkit-text-stroke-width: calc(4.5em / 16);
}
.text-outline-xxl {
    -webkit-text-stroke-width: calc(5.5em / 16);
}
.text-outline-medium {
    -webkit-text-stroke-width: calc(3.5em / 16);
}

.border-gradient
{
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    background: linear-gradient(to right,#A3C6BC 10%,  #C7D9DF 20%, #DBE1E6 100%); /* Adjust gradient as needed */

    mask-size: cover;
    mask-repeat: no-repeat;
}

.background-gradient
{
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: -3;
    background: linear-gradient(to right,#000 ,#000 ); /* Adjust gradient as needed */
    /*background: url("../../public/assets/Border-Assets/OP02-010.png") no-repeat ;*/
    /*background-size: cover;*/

    mask-size: cover;
    mask-repeat: no-repeat;
}
.background-gradient-white
{
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: -3;
    background: linear-gradient(to right,#fff ,#fff ); /* Adjust gradient as needed */
    /*background: url("../../public/assets/Border-Assets/OP02-010.png") no-repeat ;*/
    /*background-size: cover;*/

    mask-size: cover;
    mask-repeat: no-repeat;
}

.border-bottom-gradient
{
    position: absolute;
    width: 100%;
    height: 100%;
    /*z-index: 0;*/
    /*background: linear-gradient(to right,#000 ,#000 ); !* Adjust gradient as needed *!*/
    /*mask-image: url("../../public/assets/Border-Assets/Inner-Border-Bottom.png");*/
    mask-size: cover;
    mask-repeat: no-repeat;
}
.border-bottom-gradient-svg
{
    position: absolute;
    width: 100%;
    height: 100%;
    /*z-index: 0;*/
    /*background: linear-gradient(to right,#000 ,#000 ); !* Adjust gradient as needed *!*/
    /*mask-image: url("../../public/assets/Border-Assets/Inner-Border-Bottom.png");*/;
}

.border-gradient-shadow
{
    position: absolute;
    width: 100.7%;
    height: 100.5%;
    z-index: 0;
    left: -0.3%;
    top: -0.3%;
    /*background: linear-gradient(to right,#000 ,#000 ); !* Adjust gradient as needed *!*/
    /*mask-image: url("../../public/assets/Border-Assets/Inner-Border-Bottom.png");*/
    mask-size: cover;
    mask-repeat: no-repeat;
}
.border-gradient-shadow-svg
{
    position: absolute;
    width: 100.7%;
    height: 100.5%;
    z-index: 0;
    left: -0.3%;
    top: -0.3%;
}

.border-bottom-gradient-shadow
{
    position: absolute;
    width: 100.7%;
    height: 86.4%;
    z-index: 0;
    left: -0.3%;
    top: -0.57%;
    /*background: linear-gradient(to right,#000 ,#000 ); !* Adjust gradient as needed *!*/
    /*mask-image: url("../../public/assets/Border-Assets/Inner-Border-Bottom.png");*/
    mask-size: cover;
    mask-repeat: no-repeat;
}

.background-gradient-2
{
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: -3;

    mask-size: cover;
    mask-repeat: no-repeat;
}

.cost-outline {
    background-clip: text;
    -webkit-text-stroke-color: transparent;
    -webkit-background-clip: text;
}

.color-wheel-frame
{
    bottom: 1%;
    left: 2.1%;
    position: absolute;

    z-index: 105;
    /*background: url("../../public/assets/Border-Assets/OP02-010.png") no-repeat ;*/
    /*background-size: cover;*/
    width: 10.55%;
    height: 8.55%;

    mask-size: cover;
    mask-repeat: no-repeat;
}
.color-wheel-outline
{
    bottom: 0.95%;
    left: 2.1%;
    position: absolute;

    z-index: 105;
    /*background: url("../../public/assets/Border-Assets/OP02-010.png") no-repeat ;*/
    /*background-size: cover;*/
    width: 10.95%;
    height: 8.65%;

    mask-size: cover;
    mask-repeat: no-repeat;
}
.color-wheel-frame-shadow
{
    bottom: 0.7%;
    left: 1.75%;
    position: absolute;
    background: linear-gradient(to right,#000 ,#000 ); /* Adjust gradient as needed */
    z-index: 80;
    opacity: 20%;
    /*background: url("../../public/assets/Border-Assets/OP02-010.png") no-repeat ;*/
    /*background-size: cover;*/
    width: 11.25%;
    height: 9.15%;

    mask-size: cover;
    mask-repeat: no-repeat;
}
.color-wheel-highlights
{
    bottom: 1%;
    left: 2.1%;
    position: absolute;
    background: linear-gradient(to right,#fff ,#fff ); /* Adjust gradient as needed */
    opacity: 0.3;
    z-index: 101;
    /*background: url("../../public/assets/Border-Assets/OP02-010.png") no-repeat ;*/
    /*background-size: cover;*/
    width: 10.55%;
    height: 8.55%;

    mask-size: cover;
    mask-repeat: no-repeat;
}
.color-wheel-shadows
{
    bottom: 1%;
    left: 2.1%;
    position: absolute;
    background: linear-gradient(to right,#000 ,#000 ); /* Adjust gradient as needed */
    opacity: 0.3;
    z-index: 100;
    /*background: url("../../public/assets/Border-Assets/OP02-010.png") no-repeat ;*/
    /*background-size: cover;*/
    width: 10.55%;
    height: 8.55%;

    mask-size: cover;
    mask-repeat: no-repeat;
}

.color-wheel-triangles
{
    bottom: 1%;
    left: 2.1%;
    position: absolute;

    z-index: 98;
    /*background: url("../../public/assets/Border-Assets/OP02-010.png") no-repeat ;*/
    /*background-size: cover;*/
    width: 10.55%;
    height: 8.55%;

    mask-size: cover;
    mask-repeat: no-repeat;
}

.color-wheel-triangle
{

    position: absolute;
    bottom: 1%;
    left: 2.1%;
    z-index: 98;
    /*background: url("../../public/assets/Border-Assets/OP02-010.png") no-repeat ;*/
    /*background-size: cover;*/
    width: 10.55%;
    height: 8.55%;

    mask-size: cover;
    mask-repeat: no-repeat;
}
