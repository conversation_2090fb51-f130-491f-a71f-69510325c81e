import puppeteer from "@cloudflare/puppeteer";
import { DownloadState } from './types/types';

interface Env {
	MYBROWSER: Fetcher;
	NEXT_PUBLIC_REDIRECT_URL: string;
	PUPPETEER_MAX_DURATION?: string;
}


const waitForStylesAndFonts = async (
	page: any,
	imageUrl: string,
	backgroundImageUrl: string
) => {
	await page.evaluate(
		async (imageDataURI: string, backgroundImageDataURI: string) => {
			const img: HTMLImageElement | null = document.querySelector(".cardScreenshot");
			const backgroundImg: HTMLImageElement | null = document.querySelector(
				".card-background-image",
			);

			document.body.style.background = "transparent";

			const imagePromises: Promise<void>[] = [];

			if (img) {
				const imagePromise = new Promise<void>((resolve) => {
					img.onload = () => resolve();
					img.onerror = () => resolve();
					img.src = imageDataURI;
				});
				imagePromises.push(imagePromise);
			}

			if (backgroundImg) {
				const bgImagePromise = new Promise<void>((resolve) => {
					backgroundImg.onload = () => resolve();
					backgroundImg.onerror = () => resolve();
					backgroundImg.src = backgroundImageDataURI;
				});
				imagePromises.push(bgImagePromise);
			}

			await Promise.all(imagePromises);

			if ('fonts' in document) {
				await (document as any).fonts.ready;
			}

			const styleSheets = Array.from(document.styleSheets);
			await Promise.all(
				styleSheets.map(sheet => {
					if (sheet.href) {
						return new Promise<void>((resolve) => {
							const link = document.querySelector(`link[href="${sheet.href}"]`);
							if (link) {
								link.addEventListener('load', () => resolve());
								link.addEventListener('error', () => resolve());
							} else {
								resolve();
							}
						});
					}
					return Promise.resolve();
				})
			);

			const cardElement = document.querySelector('.cardScreenshot');
			if (cardElement) {
				let attempts = 0;
				while (attempts < 50) {
					const computedStyle = window.getComputedStyle(cardElement);
					if (computedStyle.fontFamily && computedStyle.fontFamily !== 'initial') {
						break;
					}
					await new Promise(resolve => setTimeout(resolve, 100));
					attempts++;
				}
			}

			return true;
		},
		imageUrl,
		backgroundImageUrl,
	);
};

export const generateImage = async (
	url: string,
	card: DownloadState,
	env: Env
): Promise<ArrayBuffer> => {
	const browser = await puppeteer.launch(env.MYBROWSER);

	try {
		const page = await browser.newPage();
		await page.goto(url, { timeout: 30000, waitUntil: "networkidle0" });
		await page.addStyleTag({
			content: `
        body {
            overflow: hidden !important;
        }
        /* For WebKit browsers (Chrome, Safari, used by Puppeteer) */
        ::-webkit-scrollbar {
            display: none !important;
        }
        /* For Firefox (good for broader compatibility if ever needed outside Puppeteer) */
        html {
            scrollbar-width: none !important;
        }
    `
		});
		await page.waitForSelector("body", { timeout: 30000 });
		await page.waitForSelector(".cardScreenshot", { timeout: 30000 });
		await page.waitForSelector(".card-background-image", { timeout: 30000 });

		await page.setViewport(
			card.printReady
				? { width: 3677, height: 5011 }
				: { width: 3357, height: 4692 },
		);

		await waitForStylesAndFonts(page, card.imageUrl, card.backgroundImageUrl);

		const imageBuffer = await page.screenshot({ omitBackground: true });
		await browser.close();

		// Create a new ArrayBuffer from the Uint8Array
		const arrayBuffer = new ArrayBuffer(imageBuffer.length);
		const view = new Uint8Array(arrayBuffer);
		view.set(imageBuffer);

		return arrayBuffer;
	} catch (error) {
		console.error("Error in generateImage:", error);
		try {
			await browser.close();
		} catch (closeError) {
			console.error("Error closing browser:", closeError);
		}
		throw error;
	}
};
