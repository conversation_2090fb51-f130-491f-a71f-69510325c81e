{"name": "ultimatetcgcm-cf-worker", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "dev-puppeteer": "wrangler dev --x-remote-bindings", "start": "wrangler dev", "test": "vitest", "cf-typegen": "wrangler types"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.19", "puppeteer": "^24.17.0", "typescript": "^5.5.2", "vitest": "~3.2.0", "wrangler": "^4.32.0"}, "dependencies": {"@cloudflare/puppeteer": "^1.0.4", "@supabase/supabase-js": "^2.56.0", "jose": "^6.0.13", "puppeteer-core": "^24.17.0"}}