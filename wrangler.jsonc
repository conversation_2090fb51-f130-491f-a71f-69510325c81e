/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "ultimatetcgcm-cf-worker",
	"main": "src/index.ts",
	"compatibility_date": "2025-08-16",
	"observability": {
		"enabled": true
	},
	"compatibility_flags": ["nodejs_compat"],
	"browser": {
		"binding": "MYBROWSER",
		"experimental_remote": true
	},
	"vars": {
//		"NEXT_PUBLIC_REDIRECT_URL": "https://ultimatetcgcm.com",
		"NEXT_PUBLIC_REDIRECT_URL": "https://preview.ultimatetcgcm.com",
		"NEXT_PUBLIC_SUPABASE_URL":"https://koiszylnuxowacrxsevn.supabase.co",
		"NEXT_PUBLIC_SUPABASE_ANON_KEY":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtvaXN6eWxudXhvd2FjcnhzZXZuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDAxNDM3MTMsImV4cCI6MjAxNTcxOTcxM30.Tuu_-zM8mVNtRwvKx074O1SSc7bdxNd7y1yryuQZW68",
		"NEXT_PUBLIC_PADDLE_API_LINK":"https://api.paddle.com",
		"NEXT_PUBLIC_PADDLE_CLIENT_TOKEN":"live_e56cf2b31fe4e42753f4efb7560",
		"NEXT_PUBLIC_PROJECT_REF": "koiszylnuxowacrxsevn"
	},
	"limits": {
		"cpu_ms": 300000
	},
	"durable_objects": {
		"bindings": [
			{
				"name": "QUEUE",
				"class_name": "ImageQueue"
			}
		]
	},
	"migrations": [
		{
			"tag": "v1",
			"new_classes": ["ImageQueue"]
		}
	]
	/**
	 * Smart Placement
	 * Docs: https://developers.cloudflare.com/workers/configuration/smart-placement/#smart-placement
	 */
	// "placement": { "mode": "smart" }
	/**
	 * Bindings
	 * Bindings allow your Worker to interact with resources on the Cloudflare Developer Platform, including
	 * databases, object storage, AI inference, real-time communication and more.
	 * https://developers.cloudflare.com/workers/runtime-apis/bindings/
	 */
	/**
	 * Environment Variables
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables
	 */
//	 "vars": { "MY_VARIABLE": "production_value" }
	/**
	 * Note: Use secrets to store sensitive data.
	 * https://developers.cloudflare.com/workers/configuration/secrets/
	 */
	/**
	 * Static Assets
	 * https://developers.cloudflare.com/workers/static-assets/binding/
	 */
	// "assets": { "directory": "./public/", "binding": "ASSETS" }
	/**
	 * Service Bindings (communicate between multiple Workers)
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#service-bindings
	 */
	// "services": [{ "binding": "MY_SERVICE", "service": "my-service" }]
}
